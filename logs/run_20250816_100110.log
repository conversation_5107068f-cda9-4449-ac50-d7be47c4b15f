========================================
运行时间: Sat Aug 16 10:01:10 AM Asia 2025
命令: python run.py --model Chinese_Bert --embedding random --mode train
========================================
Traceback (most recent call last):
  File "/home/<USER>/FP/ChiFraud/run.py", line 32, in <module>
    x = import_module('models.' + model_name)
  File "/home/<USER>/anaconda3/envs/f/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/FP/ChiFraud/models/Chinese_Bert.py", line 8, in <module>
    from transformers import BertConfig
ModuleNotFoundError: No module named 'transformers'
========================================
运行结束时间: Sat Aug 16 10:01:13 AM Asia 2025
退出状态码: 0
✅ 训练成功完成！日志已保存到: logs/run_20250816_100110.log
