========================================
运行时间: Sat Aug 16 10:02:13 AM Asia 2025
命令: python run.py --model Chinese_Bert --embedding random --mode train
========================================
Traceback (most recent call last):
  File "/home/<USER>/FP/ChiFraud/run.py", line 33, in <module>
    config = x.Config(dataset, embedding)
  File "/home/<USER>/FP/ChiFraud/models/Chinese_Bert.py", line 36, in __init__
    self.tokenizer = BertWordPieceTokenizer(vocab_file)
  File "/home/<USER>/anaconda3/envs/f/lib/python3.10/site-packages/tokenizers/implementations/bert_wordpiece.py", line 30, in __init__
    tokenizer = Tokenizer(WordPiece(vocab, unk_token=str(unk_token)))
Exception: Error while initializing WordPiece: No such file or directory (os error 2)
========================================
运行结束时间: Sat Aug 16 10:02:17 AM Asia 2025
退出状态码: 0
✅ 训练成功完成！日志已保存到: logs/run_20250816_100213.log
