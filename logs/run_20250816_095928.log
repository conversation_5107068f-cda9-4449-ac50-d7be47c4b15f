========================================
运行时间: Sat Aug 16 09:59:28 AM Asia 2025
命令: python run.py --model Chinese_Bert --embedding random --mode train
========================================
Traceback (most recent call last):
  File "/home/<USER>/FP/ChiFraud/run.py", line 27, in <module>
    from utils_chinesebert import build_dataset, build_iterator, get_time_dif
  File "/home/<USER>/FP/ChiFraud/utils_chinesebert.py", line 18, in <module>
    import tokenizers
ModuleNotFoundError: No module named 'tokenizers'
========================================
运行结束时间: Sat Aug 16 09:59:31 AM Asia 2025
退出状态码: 0
✅ 训练成功完成！日志已保存到: logs/run_20250816_095928.log
