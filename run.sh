#!/bin/bash
# model: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Transformer, <PERSON>, Chinese_Bert

# 创建必要的目录
LOG_DIR="logs"
mkdir -p "$LOG_DIR"
mkdir -p "saved_dict"

# 生成带时间戳的日志文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/run_${TIMESTAMP}.log"

echo "开始运行训练，日志将保存到: $LOG_FILE"
echo "========================================" | tee "$LOG_FILE"
echo "运行时间: $(date)" | tee -a "$LOG_FILE"
echo "命令: python run.py --model Chinese_Bert --embedding random --mode train" | tee -a "$LOG_FILE"
echo "========================================" | tee -a "$LOG_FILE"

# 运行Python脚本并将输出同时显示在终端和保存到日志文件
python run.py \
   --model Chinese_Bert \
   --embedding random \
   --mode train 2>&1 | tee -a "$LOG_FILE"

# 记录结束时间和退出状态
EXIT_CODE=$?
echo "========================================" | tee -a "$LOG_FILE"
echo "运行结束时间: $(date)" | tee -a "$LOG_FILE"
echo "退出状态码: $EXIT_CODE" | tee -a "$LOG_FILE"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ 训练成功完成！日志已保存到: $LOG_FILE" | tee -a "$LOG_FILE"
else
    echo "❌ 训练过程中出现错误，退出状态码: $EXIT_CODE" | tee -a "$LOG_FILE"
fi

exit $EXIT_CODE
